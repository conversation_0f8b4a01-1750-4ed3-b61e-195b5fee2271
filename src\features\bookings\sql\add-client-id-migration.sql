-- Add client_id field to bookings table for agency client linking
-- Run this SQL in your Supabase SQL Editor

-- Add client_id column to bookings table
ALTER TABLE public.bookings 
ADD COLUMN IF NOT EXISTS client_id UUID REFERENCES public.agency_clients(id) ON DELETE SET NULL;

-- Add index for better query performance
CREATE INDEX IF NOT EXISTS idx_bookings_client_id ON public.bookings(client_id);

-- Add comment to document the purpose
COMMENT ON COLUMN public.bookings.client_id IS 'Optional reference to agency client for agency bookings';
