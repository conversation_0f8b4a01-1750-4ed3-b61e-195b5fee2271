
import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import DashboardSummary from '@/components/dashboard/DashboardSummary';
import UpcomingBookings from '@/components/dashboard/UpcomingBookings';
import ContractsRequiringSignature from '@/components/dashboard/ContractsRequiringSignature';
import { Button } from '@/components/ui/button';
import { Plus, Filter } from 'lucide-react';
import { BookingModal } from '@/features/bookings/components';
import { useIsMobile } from '@/core';

const ArtistDashboard = () => {
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const isMobile = useIsMobile();

  // Force refresh of dashboard data when component mounts
  useEffect(() => {
    // Increment refresh key to force re-render of DashboardSummary
    setRefreshKey(prevKey => prevKey + 1);
  }, []);

  return (
    <DashboardLayout userType="artist">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <h1 className="text-2xl font-bold tracking-tight">Artist Dashboard</h1>
          <div className="flex space-x-2">

          </div>
        </div>


        <DashboardSummary key={refreshKey} userType="artist" />

        {isMobile ? (
          <div className="flex flex-col space-y-4">
            {/* On mobile, show contracts requiring signature first for better visibility */}
            <div className="flex flex-col">
              <ContractsRequiringSignature userType="artist" />
            </div>
            <div className="flex flex-col">
              <UpcomingBookings userType="artist" />
            </div>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2">
            <div className="flex flex-col space-y-6">
              <div className="flex flex-col">
                <UpcomingBookings userType="artist" />
              </div>
            </div>
            <div className="flex flex-col space-y-6">
              <div className="flex flex-col">
                <ContractsRequiringSignature userType="artist" />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Booking Modal */}
      <BookingModal
        open={isBookingModalOpen}
        onClose={() => setIsBookingModalOpen(false)}
        selectedDate={new Date()}
        userType="artist"
      />
    </DashboardLayout>
  );
};

export default ArtistDashboard;
