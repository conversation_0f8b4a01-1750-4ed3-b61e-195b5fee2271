/**
 * React hooks for managing export functionality
 *
 * This module provides a clean, extensible structure for export operations.
 * Currently supports: Bookings and Clients
 *
 * Architecture:
 * - useExport: Base hook for generic export operations
 * - useBookingExport: Specialized hook for booking exports
 * - useClientExport: Specialized hook for client exports
 *
 * To add new export types:
 * 1. Add configuration to export-configs.ts
 * 2. Create specialized hook following the pattern below
 * 3. Export from core/index.ts
 */

import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import {
  exportData,
  ExportData,
  ExportFormat,
  ExportConfig,
  ExportColumn
} from '../utils/export-utils';
import {
  getExportColumns,
  exportConfigurations,
  SupportedExportType
} from '../utils/export-configs';

export interface UseExportOptions {
  onSuccess?: (count: number, format: ExportFormat) => void;
  onError?: (error: Error) => void;
  defaultFormat?: ExportFormat;
}

export interface ExportHookReturn {
  isExporting: boolean;
  exportData: <T>(data: T[], options: ExportDataOptions) => Promise<void>;
  exportWithConfig: <T>(data: T[], configKey: SupportedExportType, format?: ExportFormat) => Promise<void>;
}

export interface ExportDataOptions {
  columns: ExportColumn<any>[];
  filename: string;
  format?: ExportFormat;
  includeTimestamp?: boolean;
}

/**
 * Hook for managing export operations
 */
export const useExport = (options: UseExportOptions = {}): ExportHookReturn => {
  const [isExporting, setIsExporting] = useState(false);
  const {
    onSuccess,
    onError,
    defaultFormat = 'csv'
  } = options;

  const handleExport = useCallback(async <T>(
    data: T[],
    exportOptions: ExportDataOptions
  ): Promise<void> => {
    setIsExporting(true);
    
    try {
      const config: ExportConfig = {
        filename: exportOptions.filename,
        format: exportOptions.format || defaultFormat,
        includeTimestamp: exportOptions.includeTimestamp ?? true,
      };

      const exportDataObj: ExportData<T> = {
        data,
        columns: exportOptions.columns,
        config,
      };

      exportData(exportDataObj);

      // Success callback
      if (onSuccess) {
        onSuccess(data.length, config.format);
      } else {
        toast.success(`Exported ${data.length} items to ${config.format.toUpperCase()}`);
      }
    } catch (error) {
      console.error('Export error:', error);
      const exportError = error instanceof Error ? error : new Error('Export failed');
      
      if (onError) {
        onError(exportError);
      } else {
        toast.error('Failed to export data');
      }
    } finally {
      setIsExporting(false);
    }
  }, [defaultFormat, onSuccess, onError]);

  const exportWithConfig = useCallback(async <T>(
    data: T[],
    configKey: SupportedExportType,
    format: ExportFormat = defaultFormat
  ): Promise<void> => {
    try {
      const columns = getExportColumns(configKey);
      const config = exportConfigurations[configKey];

      if (!config) {
        throw new Error(`No configuration found for: ${configKey}`);
      }

      const typeConfig = config.complete;
      if (!typeConfig) {
        throw new Error(`No configuration found for: ${configKey}`);
      }

      await handleExport(data, {
        columns,
        filename: typeConfig.filename,
        format,
        includeTimestamp: true,
      });
    } catch (error) {
      console.error('Export with config error:', error);
      const exportError = error instanceof Error ? error : new Error('Export configuration failed');

      if (onError) {
        onError(exportError);
      } else {
        toast.error('Failed to export data: ' + exportError.message);
      }
    }
  }, [handleExport, defaultFormat, onError]);

  return {
    isExporting,
    exportData: handleExport,
    exportWithConfig,
  };
};

// ============================================================================
// SPECIALIZED EXPORT HOOKS
// ============================================================================

/**
 * Hook specifically for booking exports
 */
export const useBookingExport = (options: UseExportOptions = {}) => {
  const exportHook = useExport(options);

  const exportBookings = useCallback(async (
    bookings: any[],
    format: ExportFormat = 'csv'
  ) => {
    await exportHook.exportWithConfig(bookings, 'bookings', format);
  }, [exportHook]);

  return {
    ...exportHook,
    exportBookings,
  };
};

/**
 * Hook specifically for client exports
 */
export const useClientExport = (options: UseExportOptions = {}) => {
  const exportHook = useExport(options);

  const exportClients = useCallback(async (
    clients: any[],
    format: ExportFormat = 'csv'
  ) => {
    await exportHook.exportWithConfig(clients, 'clients', format);
  }, [exportHook]);

  return {
    ...exportHook,
    exportClients,
  };
};
