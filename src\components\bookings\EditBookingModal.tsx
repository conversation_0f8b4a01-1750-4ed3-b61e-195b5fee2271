
import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

import { showSuccessToast, showErrorToast } from "@/core";
import { supabase } from '@/core/api/supabase';
import { fetchArtists } from '@/features/entities/api';
import { getUserEntityIdForBooking } from '@/features/entities/api/profileService';
import { useAuth } from '@/contexts/AuthContext';

// Import form section components
import {
  GeneralInformation,
  DateTimeSelection,
  ArtistSelection,
  ClientSelection,
  FinancialDetails
} from '@/features/bookings/components/form-sections';

import { ArtistData } from '@/features/entities/types';

interface Booking {
  id: string;
  title: string;
  venue_id?: string; // Direct field (fallback)
  artist_id?: string; // Direct field (fallback)
  booking_start: string;
  booking_end: string;
  description: string | null;
  pricing_type: 'fixed' | 'hourly';
  price: number;
  status: string;
  location: string;
  client_id?: string | null;
  // Nested objects from joins
  artist?: {
    id: string;
    name?: string;
    artist_name?: string;
  };
  venue?: {
    id: string;
    name?: string;
    venue_name?: string;
  };
}

interface EditBookingModalProps {
  open: boolean;
  onClose: () => void;
  booking: Booking | null;
  userType: 'venue' | 'artist' | 'agency';
  onBookingUpdated?: () => void;
}

const EditBookingModal = ({
  open,
  onClose,
  booking,
  userType,
  onBookingUpdated
}: EditBookingModalProps) => {
  const { profile } = useAuth();

  // Form state - matching new booking modal structure
  const [title, setTitle] = useState('');
  const [location, setLocation] = useState('');
  const [description, setDescription] = useState('');
  const [bookingType, setBookingType] = useState<'fixed' | 'hourly'>('fixed');
  const [price, setPrice] = useState('');
  const [loading, setLoading] = useState(false);

  // Date and time state
  const [eventDate, setEventDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [startTime, setStartTime] = useState('18:00');
  const [endTime, setEndTime] = useState('22:00');
  const [validationErrors, setValidationErrors] = useState<{dates?: string}>({});
  const [totalPrice, setTotalPrice] = useState<number | null>(null);

  // Artist state
  const [artists, setArtists] = useState<ArtistData[]>([]);
  const [agencyArtists, setAgencyArtists] = useState<ArtistData[]>([]);
  const [selectedArtist, setSelectedArtist] = useState<ArtistData | null>(null);
  const [isLoadingArtists, setIsLoadingArtists] = useState(false);

  // Client state for agencies
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);

  // Venue state
  const [venueId, setVenueId] = useState<string | null>(null);

  // Initialize form data when modal opens
  useEffect(() => {
    if (open && booking) {
      const startDate = new Date(booking.booking_start);
      const endDate = new Date(booking.booking_end);

      // Extract venue and artist IDs from nested objects or fallback to direct fields
      const venueId = booking.venue?.id || booking.venue_id;
      const artistId = booking.artist?.id || booking.artist_id;

      setTitle(booking.title);
      setLocation(booking.location);
      setDescription(booking.description || '');
      setBookingType(booking.pricing_type);
      setPrice(String(booking.price));
      setVenueId(venueId || null);
      setEventDate(startDate);
      setEndDate(endDate);
      setStartTime(format(startDate, 'HH:mm'));
      setEndTime(format(endDate, 'HH:mm'));
      setSelectedClientId(booking.client_id || null);

      console.log('Booking data:', {
        venueId,
        artistId,
        clientId: booking.client_id,
        artist: booking.artist,
        venue: booking.venue
      });
      console.log('Setting selected client ID from booking:', booking.client_id);

      // Reset artist selection when booking changes
      setSelectedArtist(null);

      loadArtists();
    }
  }, [open, booking]);

  // Debug logging for client selection
  useEffect(() => {
    if (selectedClientId) {
      console.log('Selected client ID changed to:', selectedClientId);
    }
  }, [selectedClientId]);

  // Load artists using shared function
  const loadArtists = async () => {
    try {
      setIsLoadingArtists(true);
      const artistsData = await fetchArtists();
      setArtists(artistsData);
      console.log('Loaded artists:', artistsData.length, 'artists');

      // If user is from an agency, fetch their artists
      if (userType === 'agency') {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) return;

          const { data: entityData } = await supabase
            .from('entity_users')
            .select('entity_id')
            .eq('user_id', user.id)
            .single();

          if (entityData?.entity_id) {
            const { data: agencyArtistsData } = await supabase
              .from('agency_with_artists')
              .select('*')
              .eq('agency_id', entityData.entity_id);

            if (agencyArtistsData && agencyArtistsData.length > 0) {
              const formattedAgencyArtists = agencyArtistsData.map(artist => ({
                id: artist.artist_entity_id,
                artist_name: artist.artist_name,
                name: artist.artist_name,
                profile_image_url: artist.artist_profile_image,
                genre: artist.artist_genre,
                region: artist.artist_region
              }));
              setAgencyArtists(formattedAgencyArtists);
              console.log('Loaded agency artists:', formattedAgencyArtists.length, 'artists');
            }
          }
        } catch (error) {
          console.error('Error fetching agency artists:', error);
        }
      }
    } catch (error) {
      console.error('Error loading artists:', error);
      showErrorToast('Failed to load artists');
    } finally {
      setIsLoadingArtists(false);
    }
  };

  // Set selected artist when artists are loaded
  useEffect(() => {
    if (selectedArtist === null && booking && artists.length > 0) {
      // Extract artist ID from nested object or fallback to direct field
      const artistId = booking.artist?.id || booking.artist_id;

      if (!artistId) {
        console.warn('No artist ID found in booking data');
        return;
      }

      // First try to find the artist in the main artists list
      let bookingArtist = artists.find(artist => artist.id === artistId);

      // If not found in main list and user is agency, check agency artists
      if (!bookingArtist && agencyArtists.length > 0) {
        bookingArtist = agencyArtists.find(artist => artist.id === artistId);
      }

      if (bookingArtist) {
        setSelectedArtist(bookingArtist);
        console.log('Selected booking artist:', bookingArtist);
      } else {
        console.warn('Booking artist not found in artists list. Artist ID:', artistId);
        // If artist not found, we might need to fetch it specifically
        fetchBookingArtist(artistId);
      }
    }
  }, [artists, agencyArtists, booking, selectedArtist]);

  // Fetch specific artist if not found in the main lists
  const fetchBookingArtist = async (artistId: string) => {
    try {
      const { data: artistData, error } = await supabase
        .from('entities')
        .select(`
          id,
          name,
          artist_profiles (
            entity_id,
            images,
            genre,
            region,
            is_placeholder,
            contact_email
          )
        `)
        .eq('id', artistId)
        .eq('entity_type', 'artist')
        .single();

      if (error) {
        console.error('Error fetching booking artist:', error);
        return;
      }

      if (artistData && artistData.artist_profiles) {
        const images = artistData.artist_profiles.images || [];
        const profileImage = images.length > 0 ? images[0] : null;

        const artist = {
          id: artistData.id,
          artist_name: artistData.name,
          name: artistData.name,
          profile_image_url: profileImage,
          genre: artistData.artist_profiles.genre || null,
          region: artistData.artist_profiles.region || null,
          is_placeholder: artistData.artist_profiles.is_placeholder || false,
          contact_email: artistData.artist_profiles.contact_email || null
        };

        setSelectedArtist(artist);
        console.log('Fetched and selected booking artist:', artist);
      }
    } catch (error) {
      console.error('Error fetching booking artist:', error);
    }
  };

  // Validation and submit logic
  const formatTimeForDatabase = (date: Date, timeString: string) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const newDate = new Date(date);
    newDate.setHours(hours, minutes, 0, 0);
    return newDate;
  };

  const validateBookingData = () => {
    if (validationErrors.dates) {
      showErrorToast(validationErrors.dates);
      return false;
    }

    if (!selectedArtist) {
      showErrorToast("Please select an artist");
      return false;
    }

    if (!title || !location) {
      showErrorToast('Please fill in all required fields');
      return false;
    }

    if (!price || parseFloat(price) <= 0) {
      showErrorToast('Please enter a valid price');
      return false;
    }

    if (!eventDate || !startTime || !endTime) {
      showErrorToast('Please fill in all date and time fields');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!booking || !validateBookingData()) return;

    setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        showErrorToast("You must be logged in to update a booking");
        return;
      }

      const userEntityId = await getUserEntityIdForBooking(user.id, userType);
      const bookingStart = formatTimeForDatabase(eventDate, startTime);
      const bookingEnd = formatTimeForDatabase(endDate, endTime);

      const updateData: any = {
        title,
        venue_id: venueId || userEntityId,
        artist_id: selectedArtist!.id,
        booking_start: bookingStart.toISOString(),
        booking_end: bookingEnd.toISOString(),
        description,
        pricing_type: bookingType,
        price: parseFloat(price),
        location,
        owner_entity_id: venueId || userEntityId
      };

      // Add client_id for agency bookings
      if (userType === 'agency') {
        updateData.client_id = selectedClientId;
      }

      const { error } = await supabase
        .from('bookings')
        .update(updateData)
        .eq('id', booking.id);

      if (error) throw error;

      showSuccessToast('Booking updated successfully!');
      if (onBookingUpdated) onBookingUpdated();
      onClose();
    } catch (error) {
      console.error('Error updating booking:', error);
      showErrorToast('Failed to update booking');
    } finally {
      setLoading(false);
    }
  };

  if (!booking) return null;

  return (
    <Dialog open={open} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Booking</DialogTitle>
          <DialogDescription>Update the details for this booking.</DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* General Information Section */}
          <GeneralInformation
            title={title}
            setTitle={setTitle}
            location={location}
            setLocation={setLocation}
            description={description}
            setDescription={setDescription}
          />

          {/* Date and Time Section */}
          <DateTimeSelection
            eventDate={eventDate}
            setEventDate={setEventDate}
            endDate={endDate}
            setEndDate={setEndDate}
            startTime={startTime}
            setStartTime={setStartTime}
            endTime={endTime}
            setEndTime={setEndTime}
            validationErrors={validationErrors}
            setValidationErrors={setValidationErrors}
          />

          {/* Artist Selection Section */}
          <h3 className="text-lg font-semibold">Artist</h3>
          <ArtistSelection
            selectedArtist={selectedArtist}
            setSelectedArtist={setSelectedArtist}
            artists={artists}
            agencyArtists={agencyArtists}
            userType={userType}
            isLoadingArtists={isLoadingArtists}
          />

          {/* Client Selection Section (only for agencies) */}
          <ClientSelection
            selectedClientId={selectedClientId}
            setSelectedClientId={setSelectedClientId}
            userType={userType}
          />

          {/* Financial Details Section */}
          <FinancialDetails
            bookingType={bookingType}
            setBookingType={setBookingType}
            price={price}
            setPrice={setPrice}
            eventDate={eventDate}
            endDate={endDate}
            startTime={startTime}
            endTime={endTime}
            totalPrice={totalPrice}
            setTotalPrice={setTotalPrice}
          />
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading || !!validationErrors.dates}
            className="bg-stagecloud-purple hover:bg-stagecloud-purple/90 bg-zinc-950 hover:bg-zinc-800"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Updating...
              </>
            ) : (
              'Update Booking'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditBookingModal;
