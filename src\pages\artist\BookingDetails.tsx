import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/contexts/AuthContext";
import { Badge } from "@/components/ui/badge";
import {
  MapPin, FileText, Clock, AlertTriangle,
  Mail, Upload, UserCircle, Phone, Paperclip, MoreVertical, Eye, Building2, ArrowLeft,
  Euro
} from 'lucide-react';
import { Button } from "@/components/ui/button";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { format, differenceInHours, differenceInMinutes } from "date-fns";
import { supabase, getBookingDocuments, getBookingDetails, getEntityUsers } from "@/core/api/supabase-compat";
import { toast } from 'sonner';

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import DocumentUploadModal from "@/components/documents/DocumentUploadModal";
import {
  formatDateEU,
  formatTimeEU,
  formatCurrencyEU,
  calculateTotalPrice,
  getDocumentStatus,
  getStatusBadgeVariant
} from "@/core";

interface Document {
  id: string;
  name: string;
  file_url: string;
  created_at: string;
  document_type: string;
  status: string;
}

interface Person {
  id: string;
  name?: string;
  email?: string;
  phone_number?: string;
  profile_image_url?: string;
  description?: string;
  role: string;
  type?: 'artist' | 'venue' | 'other';
  public_name?: string;
  public_email?: string;
  public_phone?: string;
}

interface EntityUser {
  user_id: string;
  user_name: string;
  email: string;
  phone_number: string | null;
  role: string;
  is_primary: boolean;
}

interface BookingDetails {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  duration: string;
  location: string;
  price: string | number;
  pricing_type: 'fixed' | 'hourly';
  status: "pending" | "confirmed" | "canceled" | "completed";
  payment_status: "paid" | "unpaid" | "partial";
  payment_due: string;
  artist: {
    id: string;
    name?: string;
    email?: string;
    profile_image_url?: string;
    description?: string;
    genre?: string[];
    artist_name?: string;
  };
  venue: {
    id: string;
    name?: string;
    address?: string;
    email?: string;
    description?: string;
    venue_type?: string;
    venue_name?: string;
    invoice_address?: string;
    company_name?: string;
    vat_number?: string;
    logo_url?: string;
    banner_url?: string;
  };
  documents: Document[];
  booking_start: string;
  booking_end: string;
  venue_id: string;
  artist_id: string;
  created_by?: string;
  people?: Person[];
  venueUsers?: EntityUser[];
}

const ArtistBookingDetails = () => {
  const { id } = useParams<{ id: string; }>();
  const { profile } = useAuth();
  const navigate = useNavigate();
  const [booking, setBooking] = useState<BookingDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);

  const [showDocumentUploadModal, setShowDocumentUploadModal] = useState(false);

  useEffect(() => {
    if (!id) return;
    const fetchBookingDetails = async () => {
      setIsLoading(true);
      try {
        // Use the new helper function to get all booking details
        const bookingDetailsData = await getBookingDetails(id);

        if (!bookingDetailsData) {
          throw new Error('Failed to fetch booking details');
        }

        const bookingData = bookingDetailsData.booking;
        const artistDetails = bookingDetailsData.artist.details;
        const artistProfileData = bookingDetailsData.artist.profile;
        const venueDetails = bookingDetailsData.venue.details;
        const venueProfileData = bookingDetailsData.venue.profile;
        const creatorData = bookingDetailsData.creator;

        // Verify that the current user is associated with the artist entity for this booking
        // Get the user's artist entity ID
        const { data: userEntityData } = await supabase
          .from('user_with_entities')
          .select('entity_id')
          .eq('user_id', profile?.id || '')
          .eq('entity_type', 'artist')
          .order('is_primary', { ascending: false })
          .limit(1);

        const userArtistEntityId = userEntityData && userEntityData.length > 0 ?
          userEntityData[0].entity_id : profile?.id;

        ('User artist entity ID:', userArtistEntityId);
        ('Booking artist ID:', bookingData.artist_id);

        if (userArtistEntityId !== bookingData.artist_id) {
          throw new Error("You don't have permission to view this booking");
        }

        // Calculate duration
        const startDate = new Date(bookingData.booking_start);
        const endDate = new Date(bookingData.booking_end);
        const hours = differenceInHours(endDate, startDate);
        const minutes = differenceInMinutes(endDate, startDate) % 60;
        const durationText = `${hours}h${minutes > 0 ? ` ${minutes}m` : ''}`;

        // Fetch people associated with the booking
        let people: Person[] = [];

        // Add the venue contact (booking creator) as the primary contact
        if (creatorData) {
          people.push({
            id: creatorData.id,
            name: creatorData.name || 'Unknown User',
            email: creatorData.email,
            phone_number: creatorData.phone_number,
            public_name: creatorData.name,
            public_email: creatorData.email,
            public_phone: creatorData.phone_number,
            role: 'Booking Manager',
            type: 'venue'
          });
        } else {
          // If no creator data, use venue as fallback
          people.push({
            id: bookingData.venue_id,
            name: venueDetails?.venue_name || venueProfileData?.name || "Venue",
            role: "Venue Contact",
            email: venueProfileData?.email,
            phone_number: venueProfileData?.phone_number,
            type: 'venue'
          });
        }

        const transformedBooking: BookingDetails = {
          id: bookingData.id,
          title: bookingData.title,
          description: bookingData.description || "",
          date: formatDateEU(new Date(bookingData.booking_start)),
          time: `${formatTimeEU(new Date(bookingData.booking_start))} - ${formatTimeEU(new Date(bookingData.booking_end))}`,
          duration: durationText,
          location: bookingData.location ? typeof bookingData.location === 'string' ? bookingData.location : venueDetails?.venue_type || "Main Stage" : venueDetails?.venue_type || "Main Stage",
          price: bookingData.price.toString(),
          pricing_type: bookingData.pricing_type as 'fixed' | 'hourly',
          status: bookingData.status as "pending" | "confirmed" | "canceled" | "completed",
          payment_status: "unpaid",
          payment_due: formatDateEU(new Date()),
          artist: {
            id: bookingData.artist_id,
            name: artistDetails?.public_name || artistProfileData?.name,
            email: artistDetails?.public_email || artistProfileData?.email,
            profile_image_url: artistDetails?.profile_image_url,
            description: artistDetails?.description,
            genre: artistDetails?.genre,
            artist_name: artistDetails?.artist_name,
          },
          venue: {
            id: bookingData.venue_id,
            name: venueProfileData?.name,
            venue_name: venueDetails?.venue_name,
            address: venueDetails?.address,
            email: venueProfileData?.email,
            description: venueDetails?.description,
            venue_type: venueDetails?.venue_type,
            // Handle invoicing information if available
            invoice_address: venueDetails?.invoice_address,
            company_name: venueDetails?.company_name,
            vat_number: venueDetails?.vat_number,
            logo_url: venueDetails?.logo_url,
            banner_url: venueDetails?.banner_url
          },
          documents: [],
          booking_start: bookingData.booking_start,
          booking_end: bookingData.booking_end,
          venue_id: bookingData.venue_id,
          artist_id: bookingData.artist_id,
          created_by: bookingData.created_by,
          people: people,
        };

        // Fetch venue users
        const venueEntityUsers = await getEntityUsers(bookingData.venue_id);
        if (venueEntityUsers) {
          transformedBooking.venueUsers = venueEntityUsers;
        }

        setBooking(transformedBooking);

        // Fetch documents
        const documentsData = await getBookingDocuments(id);
        if (!documentsData) {
          console.error("Error fetching documents or no documents found");
        } else {
          const transformedDocuments = documentsData.map((doc: any) => ({
            id: doc.id,
            name: doc.title || doc.name || 'Untitled',
            file_url: doc.file_url || '',
            created_at: doc.created_at,
            document_type: doc.document_type || 'document',
            status: doc.status || 'pending',
          }));
          setDocuments(transformedDocuments);
        }
      } catch (error: any) {
        console.error("Error fetching booking details:", error);
        setErrorMessage(error.message);
        toast.error("Failed to load booking details");
      } finally {
        setIsLoading(false);
      }
    };
    fetchBookingDetails();
  }, [id, profile?.id]);



  const refreshDocuments = async () => {
    if (!id) return;
    try {
      const documentsData = await getBookingDocuments(id);
      if (!documentsData) {
        console.error("Error fetching documents or no documents found");
      } else {
        const transformedDocuments = documentsData.map((doc: any) => ({
          id: doc.id,
          name: doc.title || doc.name || 'Untitled',
          file_url: doc.file_url || '',
          created_at: doc.created_at,
          document_type: doc.document_type || 'document',
          status: doc.status || 'pending',
        }));
        setDocuments(transformedDocuments);
      }
    } catch (error) {
      console.error("Error refreshing documents:", error);
    }
  };

  if (isLoading) {
    return <DashboardLayout userType="artist">
        <div className="flex flex-col items-center justify-center h-64">
          <div className="animate-spin h-12 w-12 border-4 border-gray-300 rounded-full border-t-blue-600" />
          <h3 className="mt-4 text-xl font-semibold text-gray-900">Loading Booking Details</h3>
        </div>
      </DashboardLayout>;
  }

  if (errorMessage) {
    return <DashboardLayout userType="artist">
        <div className="flex flex-col items-center justify-center h-64">
          <AlertTriangle className="h-12 w-12 text-red-500" />
          <h3 className="mt-2 text-xl font-semibold text-gray-900">Error Loading Booking</h3>
          <p className="mt-1 text-gray-600">{errorMessage}</p>
          <Button className="mt-4" asChild>
            <Link to="/artist/bookings">Back to Bookings</Link>
          </Button>
        </div>
      </DashboardLayout>;
  }

  if (!booking) {
    return <DashboardLayout userType="artist">
        <div className="flex flex-col items-center justify-center h-64">
          <AlertTriangle className="h-12 w-12 text-yellow-500" />
          <h3 className="mt-2 text-xl font-semibold text-gray-900">Booking Not Found</h3>
          <p className="mt-1 text-gray-600">
            The booking you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Button className="mt-4" asChild>
            <Link to="/artist/bookings">Back to Bookings</Link>
          </Button>
        </div>
      </DashboardLayout>;
  }

  return <DashboardLayout userType="artist">
      {showDocumentUploadModal && id && (
        <DocumentUploadModal
          open={showDocumentUploadModal}
          onClose={() => setShowDocumentUploadModal(false)}
          bookingId={id}
          onSuccess={() => refreshDocuments()}
        />
      )}
      <div className="space-y-6">
        <div className="space-y-4">
          {/* Top row with back button and booking info */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold tracking-tight">{booking.title}</h1>
                {/* <p className="text-muted-foreground">Booking ID: {booking.id}</p> */}
              </div>
            </div>
            {/* Desktop badge - hidden on mobile */}
            <div className="hidden sm:flex items-center gap-2">
              <Badge variant={getStatusBadgeVariant(booking.status)}>
                {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
              </Badge>
            </div>
          </div>

          {/* Mobile badge - shown only on mobile */}
          <div className="flex sm:hidden items-center">
            <Badge variant={getStatusBadgeVariant(booking.status)} className="w-full justify-center">
              {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Card className="overflow-hidden">
              <CardHeader className="border-b bg-transparent">
                <CardTitle>Event Details</CardTitle>
              </CardHeader>

              <CardContent className="p-0">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-0 divide-y sm:divide-y-0 sm:divide-x divide-gray-200">
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">Event</p>
                    <p className="font-medium">{booking.title}</p>
                  </div>
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">Date</p>
                    <p className="font-medium">{booking.date}</p>
                  </div>
                </div>

                <div className="border-t"></div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-0 divide-y sm:divide-y-0 sm:divide-x divide-gray-200">
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">Time</p>
                    <p className="font-medium">{booking.time}</p>
                  </div>
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">Duration</p>
                    <p className="font-medium">{booking.duration}</p>
                  </div>
                </div>

                <div className="border-t"></div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-0 divide-y sm:divide-y-0 sm:divide-x divide-gray-200">
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">Venue</p>
                    <p className="font-medium">{booking.venue.venue_name || booking.venue.name || "Unknown Venue"}</p>
                  </div>
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">Location</p>
                    <p className="font-medium">{booking.location || "Not specified"}</p>
                  </div>
                </div>

                {booking.description && (
                  <>
                    <div className="border-t"></div>
                    <div className="p-6">
                      <p className="text-sm text-gray-500 mb-1">Description</p>
                      <p className="font-medium">{booking.description}</p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="border-b flex flex-row items-center justify-between bg-transparent">
                <CardTitle>Documents</CardTitle>
                <div className="flex space-x-2">
                  <Button size="sm" onClick={() => setShowDocumentUploadModal(true)}>
                    <Upload className="h-4 w-4 mr-2" /> Add Document
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="p-0">
                {documents.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">#</TableHead>
                        <TableHead>Document</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {documents.map((doc, index) => (
                        <TableRow
                          key={doc.id}
                          className="cursor-pointer hover:bg-muted/50 transition-colors"
                          onClick={() => navigate(`/documents/${doc.id}`)}
                        >
                          <TableCell className="font-medium">{index + 1}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Paperclip className="h-4 w-4 text-gray-500" />
                              <span>
                                {doc.name}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="capitalize">{doc.document_type}</TableCell>
                          <TableCell>{format(new Date(doc.created_at), "MMM d, yyyy")}</TableCell>
                          <TableCell>
                            <Badge variant={getStatusBadgeVariant(doc.status)}>
                              {getDocumentStatus(doc)}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                  onClick={(e) => e.stopPropagation()} // Prevent row click when clicking the menu
                                >
                                  <span className="sr-only">Open menu</span>
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={(e) => {
                                  e.stopPropagation();
                                  navigate(`/documents/${doc.id}`);
                                }}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View Document</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-12 border-2 border-dashed border-gray-200 m-6 rounded-md">
                    <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <h3 className="font-medium text-lg mb-1">No documents yet</h3>
                    <p className="text-gray-500 mb-4">Upload contracts, riders, or other paperwork related to this booking</p>
                    <div className="flex justify-center gap-2">
                      <Button onClick={() => setShowDocumentUploadModal(true)}>
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Document
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>


          </div>

          <div className="space-y-6">
            <Card className="overflow-hidden">
              <CardHeader className="border-b bg-transparent">
                <CardTitle>Financial</CardTitle>
              </CardHeader>

              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Total Amount</p>
                    {booking.pricing_type === 'hourly' ? (
                      <>
                        <p className="text-3xl font-bold">
                          {formatCurrencyEU(calculateTotalPrice(
                            booking.price,
                            booking.pricing_type,
                            booking.booking_start,
                            booking.booking_end
                          ))}
                        </p>
                        <p className="text-sm text-gray-500">Excl. taxes</p>
                      </>
                    ) : (
                      <>
                        <p className="text-3xl font-bold">{formatCurrencyEU(booking?.price || "0")}</p>
                        <p className="text-sm text-gray-500">Excl. taxes</p>
                      </>
                    )}
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm text-gray-500 mb-1">Pricing Details</p>
                    <div className="flex items-center">
                      <Euro className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="font-medium">Type: {booking?.pricing_type === 'hourly' ? 'Hourly Rate' : 'Fixed Rate'}</span>
                    </div>
                    {booking.pricing_type === 'hourly' && (
                      <div className="flex items-center mt-1">
                        <Clock className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="font-medium">
                          Rate: {formatCurrencyEU(booking.price)}/hour • Duration: {booking.duration}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>



            <Card className="overflow-hidden">
              <CardHeader className="border-b flex flex-row items-center justify-between bg-transparent py-3">
                <CardTitle className="text-sm font-medium">Contacts</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                {booking.venueUsers && booking.venueUsers.length > 0 ? (
                  <div className="divide-y divide-gray-200">
                    {booking.venueUsers.map((user) => (
                      <div key={user.user_id} className="p-4">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarFallback className="bg-blue-100 text-blue-800">
                              {user.user_name?.charAt(0) || 'U'}
                            </AvatarFallback>
                          </Avatar>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-1.5 flex-wrap">
                              <h3 className="font-medium text-sm">{user.user_name}</h3>
                              <Badge variant="outline" className="bg-blue-50 text-blue-800 text-[10px] px-1.5 py-0 h-4">
                                {user.role}
                              </Badge>

                              {booking.created_by === user.user_id && (
                                <Badge variant="outline" className="bg-amber-50 text-amber-800 text-[10px] px-1.5 py-0 h-4">
                                  Creator
                                </Badge>
                              )}
                            </div>



                            {/* Contact info inline */}
                            <div className="flex flex-wrap gap-3 mt-1 text-xs text-gray-500">
                              <div className="flex items-center gap-1">
                                <Mail className="h-3 w-3" />
                                <span className="truncate">{user.email || 'No email available'}</span>
                              </div>
                              {user.phone_number && (
                                <div className="flex items-center gap-1">
                                  <Phone className="h-3 w-3" />
                                  <span>{user.phone_number}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <UserCircle className="h-8 w-8 text-gray-300 mx-auto mb-1" />
                    <p className="text-gray-500 text-sm">No venue contacts found</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="border-b bg-transparent">
                <CardTitle>Venue</CardTitle>
              </CardHeader>

              {/* Banner image placed below header with 1:4 ratio */}
              {booking.venue.banner_url && (
                <div className="relative w-full overflow-hidden border-b pb-[25%]">
                  <img
                    src={booking.venue.banner_url}
                    alt={booking.venue.venue_name || booking.venue.name || "Venue"}
                    className="absolute top-0 left-0 w-full h-full object-cover"
                  />
                </div>
              )}

              <CardContent className="p-6">
                <div className="flex flex-col space-y-4">
                  {/* Profile image and venue name with improved layout */}
                  <div className="flex items-center space-x-4">
                    <Avatar className="h-16 w-16 border-2 border-white shadow-md flex-shrink-0">
                      {booking.venue.logo_url ? (
                        <AvatarImage src={booking.venue.logo_url} alt={booking.venue.venue_name || booking.venue.name || "Venue"} />
                      ) : (
                        <AvatarFallback className="bg-blue-100 text-blue-800 text-lg font-semibold">
                          {(booking.venue.venue_name || booking.venue.name || "V").charAt(0)}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-xl truncate">{booking.venue.venue_name || booking.venue.name || "Unknown Venue"}</h3>
                      {booking.venue.venue_type && (
                        <p className="text-sm text-gray-500">{booking.venue.venue_type}</p>
                      )}
                    </div>
                  </div>

                  {booking.venue.address && (
                    <div className="flex items-start space-x-2 bg-gray-50 p-3 rounded-md">
                      <MapPin className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{booking.venue.address}</span>
                    </div>
                  )}

                  {booking.venue.description && (
                    <div className="pt-3 border-t">
                      <p className="text-sm font-medium text-gray-700 mb-2">About</p>
                      <p className="text-sm text-gray-600 leading-relaxed">{booking.venue.description}</p>
                    </div>
                  )}

                  {/* Invoicing Information */}
                  {(booking.venue.invoice_address || booking.venue.company_name || booking.venue.vat_number) && (
                    <div className="pt-3 mt-1 border-t">
                      <p className="text-sm font-medium text-gray-700 mb-2">Invoicing Information</p>
                      <div className="bg-gray-50 p-3 rounded-md space-y-2">
                        {booking.venue.company_name && (
                          <div className="flex items-start">
                            <Building2 className="h-4 w-4 text-gray-500 mt-0.5 mr-2 flex-shrink-0" />
                            <span className="text-sm">{booking.venue.company_name}</span>
                          </div>
                        )}
                        {booking.venue.vat_number && (
                          <div className="flex items-start">
                            <span className="text-sm font-medium w-28 flex-shrink-0">VAT Number:</span>
                            <span className="text-sm">{booking.venue.vat_number}</span>
                          </div>
                        )}
                        {booking.venue.invoice_address && (
                          <div className="flex items-start">
                            <span className="text-sm font-medium w-28 flex-shrink-0">Invoice Address:</span>
                            <span className="text-sm">{booking.venue.invoice_address}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>;
};

export default ArtistBookingDetails;
