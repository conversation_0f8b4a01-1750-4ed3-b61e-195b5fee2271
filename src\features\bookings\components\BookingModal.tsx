import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { showSuccessToast, showErrorToast, replaceVariablesInTemplate } from '@/core';
import { supabase } from '@/core/api/supabase';

// Import form section components
import {
  GeneralInformation,
  DateTimeSelection,
  ArtistSelection,
  ClientSelection,
  FinancialDetails,
  ContractSection
} from './form-sections';

// Import API services
import {
  fetchContractTemplates,
  createBooking,
  createContract as createContractAPI,
  getVenueDetails
} from '@/features/bookings/api';

import { fetchArtists } from '@/features/entities/api';
import { getUserEntityIdForBooking } from '@/features/entities/api/profileService';
import { ArtistData } from '@/features/entities/types';

interface BookingModalProps {
  open: boolean;
  onClose: () => void;
  selectedDate: Date;
  userType: 'venue' | 'artist' | 'agency';
}

interface ContractTemplate {
  id: string;
  title: string;
  content: string;
}

const BookingModal = ({ open, onClose, selectedDate, userType }: BookingModalProps) => {
  const [title, setTitle] = useState('');
  const [location, setLocation] = useState('');
  const [description, setDescription] = useState('');
  const [bookingType, setBookingType] = useState<'fixed' | 'hourly'>('fixed');
  const [price, setPrice] = useState('');
  const [loading, setLoading] = useState(false);
  const [artists, setArtists] = useState<ArtistData[]>([]);
  const [agencyArtists, setAgencyArtists] = useState<ArtistData[]>([]);
  const [selectedArtist, setSelectedArtist] = useState<ArtistData | null>(null);
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
  const [createContract, setCreateContract] = useState(false);
  const [isLoadingArtists, setIsLoadingArtists] = useState(false);
  const [venueId, setVenueId] = useState<string | null>(null);
  const [venueDetails, setVenueDetails] = useState<any>(null);
  const [eventDate, setEventDate] = useState<Date>(selectedDate);
  const [startTime, setStartTime] = useState('18:00');
  const [endTime, setEndTime] = useState('22:00');
  const [endDate, setEndDate] = useState<Date>(selectedDate);
  const [contractTemplates, setContractTemplates] = useState<ContractTemplate[]>([]);
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<{
    dates?: string;
  }>({});
  const [totalPrice, setTotalPrice] = useState<number | null>(null);

  useEffect(() => {
    if (open) {
      setEventDate(selectedDate);
      setEndDate(selectedDate);
      setStartTime('18:00');
      setEndTime('22:00');
      setArtists([]);
      loadInitialData();
    }
  }, [open, selectedDate]);

  const loadInitialData = async () => {
    try {
      setLoading(true);

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        showErrorToast("You must be logged in to create a booking");
        return;
      }

      // Load artists
      setIsLoadingArtists(true);
      const artistsData = await fetchArtists();
      setArtists(artistsData);

      // If user is from an agency, fetch their artists
      if (userType === 'agency') {
        try {
          // Get the user's entity ID
          const { data: entityData } = await supabase
            .from('entity_users')
            .select('entity_id')
            .eq('user_id', user.id)
            .single();

          if (entityData?.entity_id) {
            // Fetch agency's artists
            const { data: agencyArtistsData } = await supabase
              .from('agency_with_artists')
              .select('*')
              .eq('agency_id', entityData.entity_id);

            if (agencyArtistsData && agencyArtistsData.length > 0) {
              // Transform to match ArtistData format
              const formattedAgencyArtists = agencyArtistsData.map(artist => ({
                id: artist.artist_entity_id,
                artist_name: artist.artist_name,
                name: artist.artist_name,
                profile_image_url: artist.artist_profile_image,
                genre: artist.artist_genre,
                region: artist.artist_region
              }));

              setAgencyArtists(formattedAgencyArtists);
            }
          }
        } catch (error) {
          console.error('Error fetching agency artists:', error);
        }
      }

      setIsLoadingArtists(false);

      // Load venue details
      const venueData = await getVenueDetails(user.id, userType);
      setVenueId(venueData.id);
      setVenueDetails(venueData);

      // Load contract templates
      const templates = await fetchContractTemplates(user.id, venueData.id);
      setContractTemplates(templates);
    } catch (error) {
      console.error('Error loading initial data:', error);
      showErrorToast('Failed to load booking data');
    } finally {
      setLoading(false);
    }
  };

  const formatTimeForDatabase = (date: Date, timeString: string) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const newDate = new Date(date);
    newDate.setHours(hours, minutes, 0, 0);
    return newDate;
  };

  const validateBookingData = () => {
    if (validationErrors.dates) {
      showErrorToast(validationErrors.dates);
      return false;
    }

    if (!venueId) {
      showErrorToast("No venue selected");
      return false;
    }

    if (!selectedArtist) {
      showErrorToast("Please select an artist");
      return false;
    }

    if (!title || !location) {
      showErrorToast('Please fill in all required fields');
      return false;
    }

    if (!price || parseFloat(price) <= 0) {
      showErrorToast('Please enter a valid price');
      return false;
    }

    if (!eventDate || !startTime || !endTime) {
      showErrorToast('Please fill in all date and time fields');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateBookingData()) return;

    setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        showErrorToast("You must be logged in to create a booking");
        return;
      }

      // Get the user's entity ID for the booking
      const userEntityId = await getUserEntityIdForBooking(user.id, userType);

      if (!userEntityId && (userType === 'venue' || userType === 'agency')) {
        showErrorToast(`Could not determine your ${userType} entity. Please contact support.`);
        return;
      }

      const bookingStart = formatTimeForDatabase(eventDate, startTime);
      const bookingEnd = formatTimeForDatabase(endDate, endTime);

      // Determine the owner entity ID based on user type
      const ownerEntityId = (userType === 'venue' || userType === 'agency')
        ? (venueId || userEntityId)
        : selectedArtist.id; // If artist is creating the booking, they own it

      // Create booking data
      const bookingData = {
        title: title,
        venue_id: (userType === 'venue' || userType === 'agency') ? (venueId || userEntityId) : venueId,
        artist_id: selectedArtist.id,
        booking_start: bookingStart.toISOString(),
        booking_end: bookingEnd.toISOString(),
        description: description,
        pricing_type: bookingType,
        price: parseFloat(price),
        status: 'pending',
        location: location,
        created_by: user.id,
        owner_entity_id: ownerEntityId,
        ...(selectedClientId && { client_id: selectedClientId })
      };

      // Create the booking
      const booking = await createBooking(bookingData);

      // Create contract if needed
      if (createContract && selectedTemplateId && booking) {
        const template = contractTemplates.find(t => t.id === selectedTemplateId);

        if (template) {
          // Create booking data for variable replacement
          const templateData = {
            booking: {
              ...booking,
              title,
              description,
              notes: '',
              price,
              pricing_type: bookingType
            },
            artist: selectedArtist,
            venue: venueDetails || { venue_name: 'Venue' },
            contract: { id: booking.id }
          };

          // Format the content with variables replaced
          const formattedContent = replaceVariablesInTemplate(template.content, templateData);

          // Create contract data
          const contractData = {
            booking_id: booking.id,
            title: `Contract for ${title}`,
            document_type: 'contract',
            content: formattedContent,
            created_by: user.id,
            owner_entity_id: ownerEntityId,
            status: 'pending',
            is_signable: true
          };

          // Create the contract
          await createContractAPI(contractData);
        }
      }

      showSuccessToast('Booking created successfully!');
      resetForm();
      onClose();
    } catch (error) {
      console.error('Error creating booking:', error);
      showErrorToast('Failed to create booking');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setTitle('');
    setLocation('');
    setEventDate(new Date());
    setEndDate(new Date());
    setStartTime('18:00');
    setEndTime('22:00');
    setDescription('');
    setBookingType('fixed');
    setPrice('');
    setSelectedArtist(null);
    setSelectedClientId(null);
    setValidationErrors({});
    setCreateContract(false);
    setSelectedTemplateId(null);
  };

  return (
    <Dialog open={open} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Booking</DialogTitle>
          <DialogDescription>Create a new booking and optionally generate a contract.</DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* General Information Section */}
          <GeneralInformation
            title={title}
            setTitle={setTitle}
            location={location}
            setLocation={setLocation}
            description={description}
            setDescription={setDescription}
          />

          {/* Date and Time Section */}
          <DateTimeSelection
            eventDate={eventDate}
            setEventDate={setEventDate}
            endDate={endDate}
            setEndDate={setEndDate}
            startTime={startTime}
            setStartTime={setStartTime}
            endTime={endTime}
            setEndTime={setEndTime}
            validationErrors={validationErrors}
            setValidationErrors={setValidationErrors}
          />

          {/* Artist Selection Section */}
          <h3 className="text-lg font-semibold">Artist</h3>
          <ArtistSelection
            selectedArtist={selectedArtist}
            setSelectedArtist={setSelectedArtist}
            artists={artists}
            agencyArtists={agencyArtists}
            userType={userType}
            isLoadingArtists={isLoadingArtists}
          />

          {/* Client Selection Section (only for agencies) */}
          <ClientSelection
            selectedClientId={selectedClientId}
            setSelectedClientId={setSelectedClientId}
            userType={userType}
          />

          {/* Financial Details Section */}
          <FinancialDetails
            bookingType={bookingType}
            setBookingType={setBookingType}
            price={price}
            setPrice={setPrice}
            eventDate={eventDate}
            endDate={endDate}
            startTime={startTime}
            endTime={endTime}
            totalPrice={totalPrice}
            setTotalPrice={setTotalPrice}
          />

          {/* Contract Section */}
          <ContractSection
            createContract={createContract}
            setCreateContract={setCreateContract}
            contractTemplates={contractTemplates}
            selectedTemplateId={selectedTemplateId}
            setSelectedTemplateId={setSelectedTemplateId}
          />
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading || !!validationErrors.dates}
            className="bg-stagecloud-purple hover:bg-stagecloud-purple/90 bg-zinc-950 hover:bg-zinc-800"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              'Create Booking'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BookingModal;
