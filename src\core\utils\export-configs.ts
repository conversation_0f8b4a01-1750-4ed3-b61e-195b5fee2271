/**
 * Export configurations for supported data types
 *
 * This module provides a clean, extensible structure for defining export configurations.
 * Currently supports: Bookings and Clients
 *
 * To add new export types:
 * 1. Define the data interface
 * 2. Create export columns configuration
 * 3. Add to the registry
 * 4. Create specialized hook (optional)
 */

import { ExportColumn, commonFormatters } from './export-utils';
import { formatDateEU, formatTimeEU } from './date-utils';

// ============================================================================
// SUPPORTED EXPORT DATA TYPES
// ============================================================================

/**
 * Booking export data structure
 */
export interface BookingExportData {
  id: string;
  title: string;
  start: Date;
  end: Date;
  status: string;
  artist?: {
    artist_name?: string;
    name?: string;
    id: string;
  };
  fee?: number;
  hourlyRate?: number;
  pricing_type?: string;
  address?: string;
  description?: string;
}

/**
 * Client export data structure
 */
export interface ClientExportData {
  id: string;
  name: string;
  type: 'company' | 'person';
  email?: string;
  phone?: string;
  address?: string;
  vat_number?: string;
  notes?: string;
  tags?: string[];
  contacts?: any[];
  primary_contact_name?: string;
  primary_contact_email?: string;
  primary_contact_phone?: string;
  primary_contact_position?: string;
  total_contacts?: number;
  created_at: string;
  updated_at: string;
}

// ============================================================================
// EXPORT COLUMN CONFIGURATIONS
// ============================================================================

/**
 * Booking export columns configuration
 */
export const bookingExportColumns: ExportColumn<BookingExportData>[] = [
  {
    key: 'id',
    label: 'Booking ID',
  },
  {
    key: 'title',
    label: 'Event Title',
  },
  {
    key: 'artist',
    label: 'Artist',
    formatter: (value) => value?.artist_name || value?.name || 'Unknown',
  },
  {
    key: 'start',
    label: 'Date',
    formatter: (value) => formatDateEU(new Date(value)),
  },
  {
    key: 'start',
    label: 'Start Time',
    formatter: (value) => formatTimeEU(new Date(value)),
  },
  {
    key: 'end',
    label: 'End Time',
    formatter: (value) => formatTimeEU(new Date(value)),
  },
  {
    key: 'status',
    label: 'Status',
    formatter: commonFormatters.status,
  },
  {
    key: 'fee',
    label: 'Fee (EUR)',
    formatter: (value) => value || 0,
  },
  {
    key: 'pricing_type',
    label: 'Pricing Type',
    formatter: (value) => value || 'fixed',
  },
  {
    key: 'hourlyRate',
    label: 'Hourly Rate (EUR)',
    formatter: (value) => value || '',
  },
  {
    key: 'address',
    label: 'Address',
    formatter: (value) => value || '',
  },
  {
    key: 'description',
    label: 'Description',
    formatter: (value) => value || '',
  },
];

/**
 * Client export columns configuration
 */
export const clientExportColumns: ExportColumn<ClientExportData>[] = [
  {
    key: 'id',
    label: 'Client ID',
  },
  {
    key: 'name',
    label: 'Client Name',
  },
  {
    key: 'type',
    label: 'Type',
    formatter: (value) => value === 'company' ? 'Company' : 'Individual',
  },
  {
    key: 'email',
    label: 'Email',
    formatter: (value) => value || '',
  },
  {
    key: 'phone',
    label: 'Phone',
    formatter: (value) => value || '',
  },
  {
    key: 'address',
    label: 'Address',
    formatter: (value) => value || '',
  },
  {
    key: 'vat_number',
    label: 'VAT Number',
    formatter: (value) => value || '',
  },
  {
    key: 'tags',
    label: 'Tags',
    formatter: (value) => Array.isArray(value) ? value.join(', ') : '',
  },
  {
    key: 'total_contacts',
    label: 'Total Contacts',
    formatter: (value) => value || 0,
  },
  {
    key: 'primary_contact_name',
    label: 'Primary Contact Name',
    formatter: (value) => value || '',
  },
  {
    key: 'primary_contact_email',
    label: 'Primary Contact Email',
    formatter: (value) => value || '',
  },
  {
    key: 'primary_contact_phone',
    label: 'Primary Contact Phone',
    formatter: (value) => value || '',
  },
  {
    key: 'primary_contact_position',
    label: 'Primary Contact Position',
    formatter: (value) => value || '',
  },
  {
    key: 'notes',
    label: 'Notes',
    formatter: (value) => value || '',
  },
  {
    key: 'created_at',
    label: 'Created Date',
    formatter: commonFormatters.date,
  },
  {
    key: 'updated_at',
    label: 'Last Updated',
    formatter: commonFormatters.date,
  },
];

// ============================================================================
// EXPORT REGISTRY AND CONFIGURATION
// ============================================================================

/**
 * Supported export types for type safety
 */
export type SupportedExportType = 'bookings' | 'clients';
export type ExportVariant = 'complete';

/**
 * Export type definitions for better type safety
 */
export type BookingExportType = 'complete';
export type ClientExportType = 'complete';

/**
 * Registry of available export configurations
 *
 * To add a new export type:
 * 1. Add the type to SupportedExportType
 * 2. Add case to getExportColumns
 * 3. Add configuration to exportConfigurations
 */
export const getExportColumns = (
  dataType: SupportedExportType
): ExportColumn<any>[] => {
  switch (dataType) {
    case 'bookings':
      return bookingExportColumns;
    case 'clients':
      return clientExportColumns;
    default:
      throw new Error(`Unsupported export data type: ${dataType}`);
  }
};

/**
 * Predefined export configurations
 */
export const exportConfigurations = {
  bookings: {
    complete: {
      columns: bookingExportColumns,
      filename: 'agency-bookings',
    },
  },
  clients: {
    complete: {
      columns: clientExportColumns,
      filename: 'agency-clients',
    },
  },
} as const;
