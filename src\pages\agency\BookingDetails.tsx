import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/contexts/AuthContext";
import { Badge } from "@/components/ui/badge";
import {
  MapPin,
  FileText,
  Clock,
  CheckCircle2,
  XCircle,
  Upload,
  Mail,
  Share2,
  FileText as FileTextIcon,
  AlertCircle,
  MoreVertical,
  Eye,
  UserCircle,
  MoreHorizontal,
  Edit,
  Trash2,
  ChevronDown,
  AlertTriangle,
  Paperclip,
  Phone,
  ArrowLeft,
  Loader2,
  Plus,
  Euro,
  FileUp,
  Building2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useIsMobile } from "@/core/hooks/use-mobile";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
// The Table components are still used in the DocumentsList component
import { format, differenceInHours, differenceInMinutes } from "date-fns";
import {
  supabase,
  getBookingDocuments,
  getBookingDetails,
  getEntityUsers,
} from "@/core/api/supabase-compat";
import { toast } from "sonner";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import DocumentUploadModal from "@/components/documents/DocumentUploadModal";
import DocumentGenerateModal from "@/components/documents/DocumentGenerateModal";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import EditBookingModal from "@/components/bookings/EditBookingModal";
import {
  formatDateEU,
  formatTimeEU,
  formatCurrencyEU,
  calculateTotalPrice,
} from "@/core";
import { getDocumentStatus, getStatusBadgeVariant } from "@/core";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import CustomDocumentsList from "@/components/documents/CustomDocumentsList";

// Helper functions for formatting
const formatDate = (date: Date) => {
  return formatDateEU(date);
};

const formatTime = (date: Date) => {
  return formatTimeEU(date);
};

interface Document {
  id: string;
  name: string;
  file_url: string;
  created_at: string;
  document_type: string;
  status: string;
}

interface EntityUser {
  user_id: string;
  user_name: string;
  email: string;
  phone_number: string | null;
  role: string;
  is_primary: boolean;
}

interface BookingDetails {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  duration: string;
  location: string;
  price: string | number;
  pricing_type: "fixed" | "hourly";
  status: "pending" | "confirmed" | "canceled" | "completed";
  payment_status: "paid" | "unpaid" | "partial";
  payment_due: string;
  artist: {
    id: string;
    name?: string;
    email?: string;
    profile_image_url?: string;
    description?: string;
    genre?: string[];
    artist_name?: string;
    banner_image_url?: string;
    region?: string;
    social_links?: {
      instagram?: string;
      spotify?: string;
      soundcloud?: string;
      mixcloud?: string;
      tiktok?: string;
    };
  };
  venue: {
    id: string;
    name?: string;
    address?: string;
    email?: string;
    description?: string;
    venue_type?: string;
    venue_name?: string;
    invoice_address?: string;
    company_name?: string;
    vat_number?: string;
  };
  client?: {
    id: string;
    name: string;
    type: "company" | "person";
    email?: string;
    phone?: string;
    address?: string;
    contacts?: Array<{
      id: string;
      name: string;
      email?: string;
      phone?: string;
      position?: string;
      is_primary: boolean;
    }>;
  };
  documents: Document[];
  booking_start: string;
  booking_end: string;
  venue_id: string;
  artist_id: string;
  created_by?: string;
  artistUsers?: EntityUser[];
}

const AgencyBookingDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { profile } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  const [booking, setBooking] = useState<BookingDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [errorMessage, setErrorMessage] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [showDeleteBookingDialog, setShowDeleteBookingDialog] = useState(false);
  const [showDocumentUploadModal, setShowDocumentUploadModal] = useState(false);
  const [showDocumentGenerateModal, setShowDocumentGenerateModal] =
    useState(false);
  const [showDeleteDocumentDialog, setShowDeleteDocumentDialog] =
    useState(false);
  const [showEditBookingModal, setShowEditBookingModal] = useState(false);

  useEffect(() => {
    if (id) {
      fetchBookingDetails();
    }
  }, [id, refreshKey]);

  const fetchBookingDetails = async () => {
    try {
      setIsLoading(true);
      setErrorMessage("");

      // Fetch booking details with artist, venue, and client information
      // Using left joins instead of inner joins to ensure we get the booking even if profiles are missing
      const { data, error } = await supabase
        .from("bookings")
        .select(
          `
          id, title, description, booking_start, booking_end, status, price, pricing_type, notes, location, created_by, client_id,
          artist:artist_id (
            id,
            name
          ),
          venue:venue_id (
            id,
            name
          )
        `
        )
        .eq("id", id)
        .single();

      if (error) {
        console.error("Error fetching booking details:", error);
        setErrorMessage("Failed to load booking details. Please try again.");
        return;
      }

      if (!data) {
        setErrorMessage("Booking not found.");
        return;
      }

      // Fetch the creator user information separately
      let creatorInfo = {
        id: data.created_by,
        name: "Unknown User",
        email: "",
      };

      try {
        const { data: userData, error: userError } = await supabase
          .from("users")
          .select("id, name, email")
          .eq("id", data.created_by)
          .single();

        if (!userError && userData) {
          creatorInfo = userData;
        }
      } catch (userErr) {
        console.error("Error fetching creator user details:", userErr);
        // Continue with default creator info
      }

      // Fetch artist users if artist exists
      let artistUsers = [];
      let artistProfileData = null;

      // Use type assertion to handle potential type errors
      const artistData = data.artist as any;
      if (artistData && artistData.id) {
        // Get entity users
        artistUsers = (await getEntityUsers(artistData.id)) || [];

        // Fetch artist profile data separately
        try {
          const { data: artistProfile, error: artistProfileError } =
            await supabase
              .from("artist_profiles")
              .select("profile_image_url, genre, region")
              .eq("entity_id", artistData.id)
              .single();

          if (!artistProfileError && artistProfile) {
            artistProfileData = artistProfile;
            console.log("Successfully fetched artist profile:", artistProfile);
          } else if (artistProfileError) {
            console.error("Error fetching artist profile:", artistProfileError);
          }
        } catch (err) {
          console.error("Exception fetching artist profile:", err);
        }
      }

      // Fetch client information if client_id exists
      let clientData = null;
      if (data.client_id) {
        try {
          // Use the existing getAgencyClient function which handles the view properly
          const { getAgencyClient } = await import(
            "@/features/agency-clients/api/clientService"
          );
          clientData = await getAgencyClient(data.client_id);
          console.log("Successfully fetched client:", clientData);
        } catch (err) {
          console.error("Exception fetching client:", err);
        }
      }

      // Combine the booking data with the creator info, artist users, profile, and client
      const bookingWithDetails = {
        ...data,
        created_by: creatorInfo,
        artistUsers: artistUsers,
        artistProfile: artistProfileData,
        client: clientData,
      };

      setBooking(bookingWithDetails as any);
    } catch (err) {
      console.error("Exception in fetchBookingDetails:", err);
      setErrorMessage("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDocumentUploaded = () => {
    setShowDocumentUploadModal(false);
    setRefreshKey((prev) => prev + 1);
    toast.success("Your document has been successfully uploaded.");
  };

  const handleBookingUpdated = () => {
    setShowEditModal(false);
    setRefreshKey((prev) => prev + 1);
    toast.success("The booking has been successfully updated.");
  };

  const handleStatusChange = async (
    status: "pending" | "confirmed" | "canceled" | "completed"
  ) => {
    if (!booking) return;
    setIsSaving(true);
    try {
      const { error } = await supabase
        .from("bookings")
        .update({ status })
        .eq("id", booking.id);
      if (error) throw error;
      setBooking((prev) => (prev ? { ...prev, status } : null));
      toast.success(`Booking status updated to ${status}`);
    } catch (error: any) {
      console.error("Error updating booking status:", error);
      toast.error("Failed to update booking status");
    } finally {
      setIsSaving(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "confirmed":
        return "bg-green-500 hover:bg-green-600 text-white";
      case "pending":
        return "bg-yellow-500 hover:bg-yellow-600 text-white";
      case "completed":
        return "bg-blue-500 hover:bg-blue-600 text-white";
      case "canceled":
        return "bg-red-500 hover:bg-red-600 text-white";
      default:
        return "bg-gray-500 hover:bg-gray-600 text-white";
    }
  };

  const handleDeleteBooking = async () => {
    if (!booking) return;

    try {
      setIsDeleting(true);

      // First, check for and delete associated documents
      const { data: documents, error: docError } = await supabase
        .from("documents")
        .select("id, file_url")
        .eq("booking_id", booking.id);

      if (docError) {
        console.error("Error checking for documents:", docError);
        throw new Error("Failed to check for associated documents");
      }

      // Delete documents if they exist
      if (documents && documents.length > 0) {
        console.log(
          `Deleting ${documents.length} documents associated with booking ${booking.id}`
        );

        for (const doc of documents) {
          // Delete document record from database
          const { error: deleteDocError } = await supabase
            .from("documents")
            .delete()
            .eq("id", doc.id);

          if (deleteDocError) {
            console.error(`Error deleting document ${doc.id}:`, deleteDocError);
          }

          // Delete file from storage if it exists
          if (doc.file_url) {
            try {
              const filePath = doc.file_url.split("/").pop();
              if (filePath) {
                await supabase.storage
                  .from("documents")
                  .remove([`${booking.id}/${filePath}`]);
              }
            } catch (storageError) {
              console.error("Error deleting file from storage:", storageError);
            }
          }
        }
      }

      // Now delete the booking
      const { error } = await supabase
        .from("bookings")
        .delete()
        .eq("id", booking.id);

      if (error) {
        console.error("Error deleting booking:", error);
        throw error;
      }

      toast.success("The booking has been successfully deleted.");

      // Navigate back to bookings page
      navigate("/agency/bookings");
    } catch (err: any) {
      console.error("Exception in handleDeleteBooking:", err);
      toast.error(
        "Failed to delete booking: " + (err.message || "Unknown error")
      );
    } finally {
      setIsDeleting(false);
      setShowDeleteBookingDialog(false);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout userType="agency">
        <div className="flex items-center justify-center h-[calc(100vh-200px)]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          <span className="ml-2 text-gray-500">Loading booking details...</span>
        </div>
      </DashboardLayout>
    );
  }

  if (errorMessage || !booking) {
    return (
      <DashboardLayout userType="agency">
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {errorMessage || "Booking not found"}
          </AlertDescription>
        </Alert>
        <Button asChild>
          <Link to="/agency/bookings">Back to Bookings</Link>
        </Button>
      </DashboardLayout>
    );
  }

  const totalPrice = calculateTotalPrice(
    booking.price,
    booking.pricing_type,
    new Date(booking.booking_start),
    new Date(booking.booking_end)
  );
  const formattedTotalPrice = formatCurrencyEU(totalPrice);

  return (
    <DashboardLayout userType="agency">
      <div className="space-y-6">
        <div className="space-y-4">
          {/* Top row with back button and booking info */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold tracking-tight">
                  {booking.title}
                </h1>
                {/* <p className="text-muted-foreground">Booking ID: {booking.id}</p> */}
              </div>
            </div>
            {/* Desktop buttons - hidden on mobile */}
            <div className="hidden sm:flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    className={`${getStatusColor(booking.status)}`}
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    {booking.status.charAt(0).toUpperCase() +
                      booking.status.slice(1)}
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    className="cursor-pointer"
                    disabled={booking.status === "pending" || isSaving}
                    onClick={() => handleStatusChange("pending")}
                  >
                    <AlertCircle className="mr-2 h-4 w-4 text-yellow-500" />
                    Pending
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer"
                    disabled={booking.status === "confirmed" || isSaving}
                    onClick={() => handleStatusChange("confirmed")}
                  >
                    <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
                    Confirmed
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer"
                    disabled={booking.status === "completed" || isSaving}
                    onClick={() => handleStatusChange("completed")}
                  >
                    <CheckCircle2 className="mr-2 h-4 w-4 text-blue-500" />
                    Completed
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer"
                    disabled={booking.status === "canceled" || isSaving}
                    onClick={() => handleStatusChange("canceled")}
                  >
                    <XCircle className="mr-2 h-4 w-4 text-red-500" />
                    Canceled
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={() => setShowEditModal(true)}
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="cursor-pointer text-red-600"
                    onClick={() => setShowDeleteBookingDialog(true)}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Mobile buttons - shown only on mobile */}
          <div className="flex sm:hidden items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  className={`flex-1 ${getStatusColor(booking.status)}`}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  {booking.status.charAt(0).toUpperCase() +
                    booking.status.slice(1)}
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  className="cursor-pointer"
                  disabled={booking.status === "pending" || isSaving}
                  onClick={() => handleStatusChange("pending")}
                >
                  <AlertCircle className="mr-2 h-4 w-4 text-yellow-500" />
                  Pending
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer"
                  disabled={booking.status === "confirmed" || isSaving}
                  onClick={() => handleStatusChange("confirmed")}
                >
                  <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
                  Confirmed
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer"
                  disabled={booking.status === "completed" || isSaving}
                  onClick={() => handleStatusChange("completed")}
                >
                  <CheckCircle2 className="mr-2 h-4 w-4 text-blue-500" />
                  Completed
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer"
                  disabled={booking.status === "canceled" || isSaving}
                  onClick={() => handleStatusChange("canceled")}
                >
                  <XCircle className="mr-2 h-4 w-4 text-red-500" />
                  Canceled
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex-1">
                  <MoreHorizontal className="h-4 w-4 mr-2" />
                  Actions
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => setShowEditModal(true)}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="cursor-pointer text-red-600"
                  onClick={() => setShowDeleteBookingDialog(true)}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Card className="overflow-hidden">
              <CardHeader className="border-b bg-transparent">
                <CardTitle>Event Details</CardTitle>
              </CardHeader>

              <CardContent className="p-0">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-0 divide-y sm:divide-y-0 sm:divide-x divide-gray-200 border-b">
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">
                      AI-generated summary
                    </p>
                    {/* <p className="font-medium">{booking.ai_summary}</p> */}
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-0 divide-y sm:divide-y sm:divide-x divide-gray-200">
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">Event</p>
                    <p className="font-medium">{booking.title}</p>
                  </div>
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">Date</p>
                    <p className="font-medium">
                      {formatDate(new Date(booking.booking_start))}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-0 divide-y sm:divide-y-0 sm:divide-x divide-gray-200 border-t">
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">Time</p>
                    <p className="font-medium">
                      {formatTime(new Date(booking.booking_start))} -{" "}
                      {formatTime(new Date(booking.booking_end))}
                    </p>
                  </div>
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">Address</p>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <p className="font-medium">
                        {booking.location ||
                          booking.venue.address ||
                          "No location specified"}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {booking.description && (
              <Card className="overflow-hidden">
                <CardHeader className="border-b bg-transparent">
                  <CardTitle>Description</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <p>{booking.description}</p>
                </CardContent>
              </Card>
            )}

            <div className="space-y-6">
              <Card>
                <CardHeader className="border-b bg-transparent !flex-col !items-start !justify-start p-4">
                  {isMobile ? (
                    // Mobile layout: title and buttons stacked vertically
                    <div className="w-full space-y-4">
                      <CardTitle>Documents</CardTitle>
                      <div className="flex space-x-2 w-full">
                        <Button
                          size="sm"
                                                    variant="secondary"

                          onClick={() => setShowDocumentUploadModal(true)}
                          className="flex-1"
                        >
                          <Upload className="h-4 w-4 mr-2" /> Add Document
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setShowDocumentGenerateModal(true)}
                          className="flex-1"
                        >
                          <FileText className="h-4 w-4 mr-2" /> New Document
                        </Button>
                        
                      </div>
                    </div>
                  ) : (
                    // Desktop layout: title on left, buttons on right
                    <div className="flex items-center justify-between w-full">
                      <CardTitle>Documents</CardTitle>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                                                    variant="secondary"

                          onClick={() => setShowDocumentUploadModal(true)}
                        >
                          <Upload className="h-4 w-4 mr-2" /> Add Document
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setShowDocumentGenerateModal(true)}
                        >
                          <FileText className="h-4 w-4 mr-2" /> New Document
                        </Button>
                        
                      </div>
                    </div>
                  )}
                </CardHeader>

                <CardContent className="p-0">
                  <CustomDocumentsList
                    key={refreshKey}
                    userType="agency"
                    documentType="all"
                    bookingId={id}
                  />
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="space-y-6">
            <Card className="overflow-hidden">
              <CardHeader className="border-b bg-transparent">
                <CardTitle>Financial</CardTitle>
              </CardHeader>

              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Total Amount</p>
                    <p className="text-3xl font-bold">{formattedTotalPrice}</p>
                    <p className="text-sm text-gray-500">Excl. taxes</p>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm text-gray-500 mb-1">
                      Pricing Details
                    </p>
                    <div className="flex items-center">
                      <div className="flex items-center gap-1">
                        <span className="font-medium">
                          Type:{" "}
                          {booking.pricing_type === "hourly"
                            ? "Hourly Rate"
                            : "Fixed Rate"}
                        </span>
                      </div>
                    </div>
                    {booking.pricing_type === "hourly" && (
                      <div className="flex items-center mt-1">
                        <div className="flex items-center gap-1">
                          <span className="font-medium">
                            Rate: {formatCurrencyEU(booking.price)}/hour •
                            Duration:{" "}
                            {formatTime(new Date(booking.booking_start))} -{" "}
                            {formatTime(new Date(booking.booking_end))}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="border-b flex flex-row items-center justify-between bg-transparent py-3">
                <CardTitle className="text-sm font-medium">Artist</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="p-6 border-b">
                  {booking.artist ? (
                    <div className="flex flex-col space-y-4">
                      <div className="flex items-center space-x-4">
                        {/* Use type assertion to handle the updated data structure */}
                        {(() => {
                          const artist = booking.artist as any;
                          const artistProfile =
                            (booking as any).artistProfile || {};

                          // Get profile data from the separately fetched profile
                          const profileImage = artistProfile.profile_image_url;
                          const genre = artistProfile.genre;
                          const region = artistProfile.region;

                          return (
                            <>
                              <Avatar className="h-16 w-16 border-2 border-white shadow-md flex-shrink-0">
                                {profileImage ? (
                                  <AvatarImage
                                    src={profileImage}
                                    alt={artist.name}
                                  />
                                ) : (
                                  <AvatarFallback className="bg-blue-100 text-blue-800 text-lg font-semibold">
                                    {artist.name ? artist.name.charAt(0) : "A"}
                                  </AvatarFallback>
                                )}
                              </Avatar>
                              <div>
                                <h3 className="font-medium text-lg">
                                  {artist.name || "Unknown Artist"}
                                </h3>
                                <div className="flex flex-wrap gap-2 mt-1">
                                  {genre && genre.length > 0 && (
                                    <Badge
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {Array.isArray(genre)
                                        ? genre.join(", ")
                                        : genre}
                                    </Badge>
                                  )}
                                  {region && (
                                    <Badge
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {region}
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </>
                          );
                        })()}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <UserCircle className="h-8 w-8 text-gray-300 mx-auto mb-1" />
                      <p className="text-gray-500 text-sm">
                        No artist information available
                      </p>
                    </div>
                  )}
                </div>
                {booking.artistUsers && booking.artistUsers.length > 0 ? (
                  <div className="divide-y divide-gray-200">
                    {booking.artistUsers.map((user) => (
                      <div key={user.user_id} className="p-4">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarFallback className="bg-blue-100 text-blue-800">
                              {user.user_name?.charAt(0) || "U"}
                            </AvatarFallback>
                          </Avatar>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-1.5 flex-wrap">
                              <h3 className="font-medium text-sm">
                                {user.user_name}
                              </h3>
                              <Badge
                                variant="outline"
                                className="bg-blue-50 text-blue-800 text-[10px] px-1.5 py-0 h-4"
                              >
                                {user.role || "Member"}
                              </Badge>

                              {user.is_primary && (
                                <Badge
                                  variant="outline"
                                  className="bg-amber-50 text-amber-800 text-[10px] px-1.5 py-0 h-4"
                                >
                                  Primary
                                </Badge>
                              )}
                            </div>

                            {/* Contact info inline */}
                            <div className="flex flex-wrap gap-3 mt-1 text-xs text-gray-500">
                              <div className="flex items-center gap-1">
                                <Mail className="h-3 w-3" />
                                <span className="truncate">
                                  {user.email || "No email available"}
                                </span>
                              </div>
                              {user.phone_number && (
                                <div className="flex items-center gap-1">
                                  <Phone className="h-3 w-3" />
                                  <span>{user.phone_number}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <UserCircle className="h-8 w-8 text-gray-300 mx-auto mb-1" />
                    <p className="text-gray-500 text-sm">
                      No artist contacts found
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
            <Card className="overflow-hidden">
              <CardHeader className="border-b flex flex-row items-center justify-between bg-transparent py-3">
                <CardTitle className="text-sm font-medium">Client</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="p-6 border-b">
                  {booking.client ? (
                    <div className="flex flex-col space-y-4">
                      <div className="flex items-center space-x-4">
                        <Avatar className="h-16 w-16 border-2 border-white shadow-md flex-shrink-0">
                          <AvatarFallback className="bg-green-100 text-green-800 text-lg font-semibold">
                            {booking.client.type === "company" ? (
                              <Building2 className="h-8 w-8" />
                            ) : (
                              booking.client.name.charAt(0)
                            )}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="font-medium text-lg">
                            {booking.client.name}
                          </h3>
                          <div className="flex flex-wrap gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {booking.client.type === "company"
                                ? "Company"
                                : "Individual"}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <Building2 className="h-8 w-8 text-gray-300 mx-auto mb-1" />
                      <p className="text-gray-500 text-sm">
                        No client linked to this booking
                      </p>
                      <p className="text-gray-400 text-xs mt-1">
                        Edit the booking to link a client
                      </p>
                    </div>
                  )}
                </div>
                {booking.client &&
                booking.client.contacts &&
                booking.client.contacts.length > 0 ? (
                  <div className="divide-y divide-gray-200">
                    {booking.client.contacts.map((contact) => (
                      <div key={contact.id} className="p-4">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarFallback className="bg-green-100 text-green-800">
                              {contact.name?.charAt(0) || "C"}
                            </AvatarFallback>
                          </Avatar>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-1.5 flex-wrap">
                              <h3 className="font-medium text-sm">
                                {contact.name}
                              </h3>
                              {contact.position && (
                                <Badge
                                  variant="outline"
                                  className="bg-green-50 text-green-800 text-[10px] px-1.5 py-0 h-4"
                                >
                                  {contact.position}
                                </Badge>
                              )}

                              {contact.is_primary && (
                                <Badge
                                  variant="outline"
                                  className="bg-amber-50 text-amber-800 text-[10px] px-1.5 py-0 h-4"
                                >
                                  Primary
                                </Badge>
                              )}
                            </div>

                            {/* Contact info inline */}
                            <div className="flex flex-wrap gap-3 mt-1 text-xs text-gray-500">
                              {contact.email && (
                                <div className="flex items-center gap-1">
                                  <Mail className="h-3 w-3" />
                                  <span className="truncate">
                                    {contact.email}
                                  </span>
                                </div>
                              )}
                              {contact.phone && (
                                <div className="flex items-center gap-1">
                                  <Phone className="h-3 w-3" />
                                  <span>{contact.phone}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : booking.client ? (
                  <div className="p-4">
                    <div className="text-xs text-gray-500">
                      {booking.client.email && (
                        <div className="flex items-center gap-1 mb-1">
                          <Mail className="h-3 w-3" />
                          <span>{booking.client.email}</span>
                        </div>
                      )}
                      {booking.client.phone && (
                        <div className="flex items-center gap-1 mb-1">
                          <Phone className="h-3 w-3" />
                          <span>{booking.client.phone}</span>
                        </div>
                      )}
                      {booking.client.address && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          <span>{booking.client.address}</span>
                        </div>
                      )}
                    </div>
                  </div>
                ) : null}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <DocumentUploadModal
        open={showDocumentUploadModal}
        onClose={() => setShowDocumentUploadModal(false)}
        onSuccess={handleDocumentUploaded}
        bookingId={id}
      />

      <DocumentGenerateModal
        open={showDocumentGenerateModal}
        onClose={() => setShowDocumentGenerateModal(false)}
        onSuccess={() => setRefreshKey((prev) => prev + 1)}
        bookingId={id}
      />

      {booking && (
        <EditBookingModal
          open={showEditModal}
          onClose={() => setShowEditModal(false)}
          booking={booking as any}
          userType={"agency" as "venue" | "artist" | "agency"}
          onBookingUpdated={handleBookingUpdated}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={showDeleteBookingDialog}
        onOpenChange={setShowDeleteBookingDialog}
        onConfirm={handleDeleteBooking}
        title="Delete Booking"
        description={`Are you sure you want to delete "${booking?.title}"? This will also delete all associated documents and cannot be undone.`}
        itemType="booking"
        isDeleting={isDeleting}
      />
    </DashboardLayout>
  );
};

export default AgencyBookingDetails;
