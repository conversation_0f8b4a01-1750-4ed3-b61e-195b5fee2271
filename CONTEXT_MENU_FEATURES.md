# Enhanced Rich Text Editor - Context Menu Features

## Overview

The Enhanced Rich Text Editor now includes comprehensive right-click context menu functionality that provides quick access to formatting options, table operations, and content insertion tools. This feature significantly improves the usability and efficiency of the template editor.

## Context Menu Types

The context menu dynamically adapts based on the current selection and cursor position:

### 1. Text Selection Context Menu
**Triggered when:** Text is selected in the editor

**Features:**
- **Clipboard Operations**
  - Copy (Ctrl+C)
  - Cut (Ctrl+X)
  - Paste (Ctrl+V)

- **Text Formatting**
  - Bold (Ctrl+B)
  - Italic (Ctrl+I)
  - Underline (Ctrl+U)

- **Text Color & Highlighting**
  - Text Color picker with 8 common colors
  - Highlight color picker with 8 highlight colors
  - Reset color options

- **Text Alignment**
  - Align Left
  - Align Center
  - Align Right
  - Justify

- **List Operations**
  - Bullet List
  - Numbered List

- **Variable Insertion**
  - Categorized variable selection
  - Scrollable variable list
  - Quick insertion with automatic menu close

### 2. Table Context Menu
**Triggered when:** Cursor is inside a table

**Features:**
- **Row Operations**
  - Add Row Above
  - Add Row Below
  - Delete Row

- **Column Operations**
  - Add Column Left
  - Add Column Right
  - Delete Column

- **Cell Operations**
  - Merge Cells
  - Split Cell

- **Table Management**
  - Delete Entire Table (with warning styling)

### 3. General Context Menu
**Triggered when:** No text is selected and cursor is not in a table

**Features:**
- **Clipboard Operations**
  - Paste (Ctrl+V)

- **Content Insertion**
  - Insert Table (3x3 with headers)
  - Insert Image
  - Insert Link

- **Variable Insertion**
  - Same categorized variable selection as text menu

- **Editor Operations**
  - Undo (Ctrl+Z)
  - Redo (Ctrl+Y)

## Technical Implementation

### Context Detection
The context menu automatically detects the current editor state:
- Uses TipTap's `editor.isActive('table')` to detect table context
- Checks `selection.empty` to determine if text is selected
- Dynamically sets menu type: 'text', 'table', or 'general'

### User Experience Features
- **Smart Positioning**: Context menu appears at cursor position
- **Keyboard Shortcuts**: Displays relevant keyboard shortcuts for quick reference
- **Visual Feedback**: 
  - Disabled states for unavailable actions
  - Color-coded dangerous actions (delete operations in red)
  - Hover effects on color pickers
- **Toast Notifications**: Success/error feedback for clipboard operations
- **Automatic Cleanup**: Menu closes after action execution

### Accessibility
- Full keyboard navigation support
- Screen reader compatible
- Clear visual hierarchy with icons and labels
- Consistent with existing UI patterns

## Usage Instructions

### For Text Editing
1. Select any text in the editor
2. Right-click to open the text context menu
3. Choose from formatting, color, alignment, or list options
4. Use the variable submenu to insert dynamic content

### For Table Operations
1. Click inside any table cell
2. Right-click to open the table context menu
3. Add/remove rows and columns as needed
4. Use merge/split for complex table layouts
5. Delete entire table when no longer needed

### For General Content
1. Right-click in any empty area of the editor
2. Insert new content (tables, images, links)
3. Access clipboard operations
4. Use undo/redo for quick corrections

## Benefits

1. **Improved Efficiency**: Quick access to common operations without toolbar navigation
2. **Context-Aware**: Only shows relevant options based on current selection
3. **Professional UX**: Matches modern editor expectations
4. **Reduced Clicks**: Direct access to nested functionality
5. **Enhanced Discoverability**: Users can explore features through right-click
6. **Consistent Experience**: Familiar interaction patterns across the application

## Future Enhancements

Potential additions for future versions:
- Custom color picker for text and highlights
- Font size adjustment in context menu
- Advanced table styling options
- Custom variable creation from context menu
- Spell check suggestions integration
- Advanced clipboard operations (paste special)

## Browser Compatibility

The context menu functionality is built on:
- Radix UI Context Menu (cross-browser compatible)
- Modern Clipboard API (requires HTTPS in production)
- TipTap editor state management
- React hooks for state management

Tested and compatible with:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
