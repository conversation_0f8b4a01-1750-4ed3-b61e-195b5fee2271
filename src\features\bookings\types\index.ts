export interface BookingData {
  title: string;
  venue_id: string;
  artist_id: string;
  booking_start: string;
  booking_end: string;
  description: string;
  pricing_type: 'fixed' | 'hourly';
  price: number;
  status: string;
  location: string;
  created_by: string;
  owner_entity_id: string;
  client_id?: string; // Optional client ID for agency bookings
}

export interface ContractData {
  booking_id: string;
  title: string;
  document_type: string;
  content: string;
  created_by: string;
  owner_entity_id: string;
  status: string;
  is_signable: boolean;
}
