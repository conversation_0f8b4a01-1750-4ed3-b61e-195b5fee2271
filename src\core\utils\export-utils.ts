/**
 * Export utility functions for generating and downloading various file formats
 */

import { formatDateEU, formatTimeEU } from './date-utils';
import { formatCurrencyEU } from './currency-utils';

// Export format types
export type ExportFormat = 'csv' | 'json' | 'xlsx';

// Base export configuration
export interface ExportConfig {
  filename: string;
  format: ExportFormat;
  includeTimestamp?: boolean;
}

// Column definition for structured exports
export interface ExportColumn<T = any> {
  key: string;
  label: string;
  formatter?: (value: any, row: T) => string;
  width?: number;
}

// Export data structure
export interface ExportData<T = any> {
  data: T[];
  columns: ExportColumn<T>[];
  config: ExportConfig;
}

/**
 * Escape CSV values to handle quotes and commas
 */
export const escapeCSVValue = (value: any): string => {
  if (value === null || value === undefined) return '';
  const stringValue = String(value);
  // Escape quotes by doubling them and wrap in quotes if contains comma, quote, or newline
  if (stringValue.includes('"') || stringValue.includes(',') || stringValue.includes('\n')) {
    return `"${stringValue.replace(/"/g, '""')}"`;
  }
  return stringValue;
};

/**
 * Convert data to CSV format
 */
export const convertToCSV = <T>(exportData: ExportData<T>): string => {
  const { data, columns } = exportData;
  
  // Create headers
  const headers = columns.map(col => escapeCSVValue(col.label));
  
  // Create rows
  const rows = data.map(row => 
    columns.map(col => {
      const value = row[col.key as keyof T];
      const formattedValue = col.formatter ? col.formatter(value, row) : value;
      return escapeCSVValue(formattedValue);
    })
  );
  
  return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
};

/**
 * Convert data to JSON format
 */
export const convertToJSON = <T>(exportData: ExportData<T>): string => {
  const { data, columns } = exportData;
  
  const processedData = data.map(row => {
    const processedRow: Record<string, any> = {};
    columns.forEach(col => {
      const value = row[col.key as keyof T];
      processedRow[col.label] = col.formatter ? col.formatter(value, row) : value;
    });
    return processedRow;
  });
  
  return JSON.stringify(processedData, null, 2);
};

/**
 * Generate filename with optional timestamp
 */
export const generateFilename = (config: ExportConfig): string => {
  const { filename, format, includeTimestamp = true } = config;
  const timestamp = includeTimestamp ? `-${new Date().toISOString().split('T')[0]}` : '';
  return `${filename}${timestamp}.${format}`;
};

/**
 * Download file in browser
 */
export const downloadFile = (content: string, filename: string, mimeType: string): void => {
  const blob = new Blob([content], { type: mimeType });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

/**
 * Get MIME type for export format
 */
export const getMimeType = (format: ExportFormat): string => {
  switch (format) {
    case 'csv':
      return 'text/csv;charset=utf-8;';
    case 'json':
      return 'application/json;charset=utf-8;';
    case 'xlsx':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    default:
      return 'text/plain;charset=utf-8;';
  }
};

/**
 * Main export function that handles different formats
 */
export const exportData = <T>(exportData: ExportData<T>): void => {
  const { config } = exportData;
  const filename = generateFilename(config);
  const mimeType = getMimeType(config.format);
  
  let content: string;
  
  switch (config.format) {
    case 'csv':
      content = convertToCSV(exportData);
      break;
    case 'json':
      content = convertToJSON(exportData);
      break;
    default:
      throw new Error(`Unsupported export format: ${config.format}`);
  }
  
  downloadFile(content, filename, mimeType);
};

// Common formatters for reuse
export const commonFormatters = {
  currency: (value: any) => formatCurrencyEU(value || 0),
  date: (value: any) => value ? formatDateEU(new Date(value)) : '',
  time: (value: any) => value ? formatTimeEU(new Date(value)) : '',
  status: (value: any) => value ? value.charAt(0).toUpperCase() + value.slice(1) : '',
  boolean: (value: any) => value ? 'Yes' : 'No',
  array: (value: any) => Array.isArray(value) ? value.join(', ') : '',
};
