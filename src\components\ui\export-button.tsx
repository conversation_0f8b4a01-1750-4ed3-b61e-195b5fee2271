/**
 * Reusable export button component with dropdown for different formats
 */

import React from 'react';
import { Button } from './button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from './dropdown-menu';
import { Download, ChevronDown, Loader2, FileText, Database } from 'lucide-react';
import { ExportFormat } from '@/core/utils/export-utils';

export interface ExportOption {
  label: string;
  value: string;
  format: ExportFormat;
  description?: string;
  icon?: React.ReactNode;
}

export interface ExportButtonProps {
  onExport: (option: ExportOption) => void;
  isExporting?: boolean;
  disabled?: boolean;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
  options?: ExportOption[];
  label?: string;
  fullWidth?: boolean;
}

// Default export options
const defaultExportOptions: ExportOption[] = [
  {
    label: 'Export as CSV',
    value: 'csv',
    format: 'csv',
    description: 'Comma-separated values file',
    icon: <FileText className="h-4 w-4" />,
  },
  {
    label: 'Export as JSON',
    value: 'json',
    format: 'json',
    description: 'JavaScript Object Notation file',
    icon: <Database className="h-4 w-4" />,
  },
];

export const ExportButton: React.FC<ExportButtonProps> = ({
  onExport,
  isExporting = false,
  disabled = false,
  variant = 'outline',
  size = 'default',
  className = '',
  options = defaultExportOptions,
  label = 'Export',
  fullWidth = false,
}) => {
  const buttonClassName = fullWidth ? `w-full ${className}` : className;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant={variant} 
          size={size}
          disabled={isExporting || disabled}
          className={buttonClassName}
        >
          {isExporting ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Download className="h-4 w-4 mr-2" />
          )}
          {label}
          <ChevronDown className="h-4 w-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className={fullWidth ? "w-full" : ""}>
        <DropdownMenuLabel>Export Options</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {options.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => onExport(option)}
            disabled={isExporting}
            className="flex items-center gap-2"
          >
            {option.icon}
            <div className="flex flex-col">
              <span>{option.label}</span>
              {option.description && (
                <span className="text-xs text-muted-foreground">
                  {option.description}
                </span>
              )}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

// Specialized export button for bookings
export interface BookingExportButtonProps extends Omit<ExportButtonProps, 'options' | 'onExport'> {
  onExport: (format: ExportFormat) => void;
}

export const BookingExportButton: React.FC<BookingExportButtonProps> = ({
  onExport,
  ...props
}) => {
  const bookingOptions: ExportOption[] = [
    {
      label: 'Export as CSV',
      value: 'csv',
      format: 'csv',
      icon: <FileText className="h-4 w-4" />,
    },
    {
      label: 'Export as JSON',
      value: 'json',
      format: 'json',
      icon: <Database className="h-4 w-4" />,
    },
  ];

  const handleExport = (option: ExportOption) => {
    onExport(option.format);
  };

  return (
    <ExportButton
      {...props}
      options={bookingOptions}
      onExport={handleExport}
    />
  );
};

// Simple export button for single format
export interface SimpleExportButtonProps extends Omit<ExportButtonProps, 'options' | 'onExport'> {
  onExport: () => void;
  format?: ExportFormat;
}

export const SimpleExportButton: React.FC<SimpleExportButtonProps> = ({
  onExport,
  format = 'csv',
  label,
  ...props
}) => {
  const formatLabel = label || `Export as ${format.toUpperCase()}`;
  
  return (
    <Button
      variant={props.variant || 'outline'}
      size={props.size}
      disabled={props.isExporting || props.disabled}
      className={props.fullWidth ? `w-full ${props.className || ''}` : props.className}
      onClick={onExport}
    >
      {props.isExporting ? (
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
      ) : (
        <Download className="h-4 w-4 mr-2" />
      )}
      {formatLabel}
    </Button>
  );
};
